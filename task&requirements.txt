Fitur yang Akan Dibuat
Berikut adalah daftar fitur utama yang direncanakan untuk pengembangan aplikasi:
●	Modul Otentikasi & Otorisasi:
o	Registrasi dan login pengguna.
o	Manajemen peran (Pemilik dan Penyewa Kos).
o	Pembatasan akses berdasarkan peran.
o	Reset kata sandi dan mengubah data pada profil pengguna.
●	Modul Manajemen Kos:
o	Fitur 1.1: <PERSON><PERSON>bah, men<PERSON><PERSON>, dan menghapus data kos yang meliputi nama, alama<PERSON>, harga, fasilitas, dan foto.
o	Fitur 1.2: Pencarian kos dengan filter lokasi, harga, dan fasilitas
o	Fitur 1.3: Tampilan Google Maps (Menggunakan Google Maps API)
●	Modul Manajemen Pemesanan:
o	Fitur 2.1: Booking kos dengan memilih tanggal masuk beserta durasi sewa
o	Fitur 2.2: Status Pemesanan (Pending, Dikonfirmasi, Dibatalkan)
o	Fitur 2.3: Notifikasi email ke penyewa dan pemilik kos
●	Modul Manajemen Pembayaran:
o	Integrasi Pembayaran Online (QRIS)
o	Histori Transaksi
●	Modul Ulasan dan Rating:
o	Penyewa dapat memberikan ulasan dan rating untuk kos
●	Laporan dan Analitik:
o	Dashboard Pemilik Kos: Laporan pendapatan bulanan dan okupansi kos
o	Grafik tren pemesanan dan statistik penyewa 
●	Pengaturan Umum:
o	Manajemen kebijakan sewa yang meliputi syarat kontrak dan denda keterlambatan


Desain Database (ERD - Entity Relationship Diagram)
Berikut adalah representasi kasar Desain Database (ERD) untuk aplikasi ini. Mohon dicatat bahwa ini adalah gambaran awal dan dapat berkembang selama proses pengembangan.
Tabel users
●	userId (INT, Primary Key, Auto Increment)
●	name (VARCHAR(255))
●	email (VARCHAR(255), Unique)
●	password (VARCHAR(10))
●	role (boolean)
●	created_at (TIMESTAMP)
●	updated_at (TIMESTAMP)
Tabel kos
●	kosId (INT, Primary Key, Auto Increment)
●	pemilikId (INT, Foreign Key ke users_id)
●	namakos (VARCHAR(255))
●	alamat (TEXT)
●	harga (DECIMAL(10,2))
●	fasilitas (Long Variable Characters)
●	foto (IMAGE)
●	status (BOOLEAN)
Tabel bookings
●	bookingId (INT, Primary Key, Auto Increment)
●	userId (INT, Foreign Key ke users.id)
●	kosId (INT, Foreign Key ke kos.id)
●	tanggalMulai (DATE)
●	durasi (INT)
●	totalHarga (DECIMAL(10,2))
●	statusPemesanan (ENUM('pending','confirmed','canceled’))
Tabel Payments
●	paymentId (INT, Primary Key, Auto Increment)
●	bookingId (INT, Foreign Key ke bookings.id)
●	metodePembayaran (ENUM(‘QRIS’))
●	jumlah (DECIMAL(10,2))
●	status (BOOLEAN)
●	tanggal (DATE)
Tabel Reviews
●	reviewId (INT, Primary Key, Auto Increment)
●	userId (INT, Foreign Key ke users.id)
●	kosId (INT, Foreign Key ke kos.id)
●	rating (INT)
●	ulasan (TEXT)
●	tanggal (DATE)

Relasi
●	Tabel users berelasi dengan tabel kos melalui kolom userID (One-to-many).
●	Tabel users berelasi dengan tabel bookings melalui kolom userID (One-to-many).
●	Tabel kos berelasi dengan tabel bookings melalui kolom kosID (One-to-many).
●	Tabel bookings berelasi dengan tabel payments melalui kolom bookingID (One-to-one).
●	Tabel users berelasi dengan tabel reviews melalui kolom userID (One-to-many).
●	Tabel kos berelasi dengan tabel reviews melalui kolom kosID (One-to-many).
